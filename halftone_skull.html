<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Halftone Skull</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            overflow: hidden;
            font-family: 'Arial', sans-serif;
        }
        
        canvas {
            display: block;
            cursor: crosshair;
        }
        
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #ffffff;
            font-size: 14px;
            opacity: 0.8;
            z-index: 100;
            text-shadow: 0 0 10px rgba(255,255,255,0.5);
        }
    </style>
</head>
<body>
    <div class="info">HALFTONE SKULL - Move mouse to interact</div>
    <canvas id="canvas"></canvas>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        let mouse = { x: canvas.width / 2, y: canvas.height / 2 };
        let dots = [];
        let time = 0;
        
        class HalftoneDot {
            constructor(x, y, baseSize, intensity) {
                this.x = x;
                this.y = y;
                this.baseSize = baseSize;
                this.size = baseSize;
                this.intensity = intensity;
                this.originalIntensity = intensity;
                this.phase = Math.random() * Math.PI * 2;
                this.pulseSpeed = 0.02 + Math.random() * 0.03;
                this.glowIntensity = 0;
            }
            
            update() {
                // Pulsing effect
                const pulse = Math.sin(time * this.pulseSpeed + this.phase) * 0.3 + 1;
                this.size = this.baseSize * pulse * this.intensity;
                
                // Mouse interaction
                const dx = this.x - mouse.x;
                const dy = this.y - mouse.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 150) {
                    const force = (150 - distance) / 150;
                    this.size *= (1 + force * 2);
                    this.glowIntensity = force * 0.8;
                    this.intensity = this.originalIntensity * (1 + force);
                } else {
                    this.glowIntensity *= 0.95;
                    this.intensity = this.originalIntensity;
                }
            }
            
            draw() {
                if (this.size < 0.5) return;
                
                ctx.save();
                
                // Main dot
                const alpha = Math.min(1, this.intensity);
                ctx.globalAlpha = alpha;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                
                // Glow effect
                if (this.glowIntensity > 0) {
                    ctx.globalAlpha = this.glowIntensity * 0.5;
                    ctx.shadowBlur = 20;
                    ctx.shadowColor = '#00ffff';
                    ctx.fillStyle = '#00ffff';
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size * 1.5, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.shadowBlur = 0;
                }
                
                ctx.restore();
            }
        }
        
        function createSkullPattern() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const dotSpacing = 8;
            const maxRadius = 200;
            
            for (let x = -maxRadius; x <= maxRadius; x += dotSpacing) {
                for (let y = -maxRadius; y <= maxRadius; y += dotSpacing) {
                    const worldX = centerX + x;
                    const worldY = centerY + y;
                    
                    // Calculate skull shape intensity
                    let intensity = getSkullIntensity(x, y);
                    
                    if (intensity > 0.1) {
                        const baseSize = intensity * 6 + 1;
                        dots.push(new HalftoneDot(worldX, worldY, baseSize, intensity));
                    }
                }
            }
        }
        
        function getSkullIntensity(x, y) {
            let intensity = 0;
            
            // Main skull shape (elliptical)
            const skullA = 120; // width
            const skullB = 140; // height
            const skullDist = (x*x)/(skullA*skullA) + ((y+20)*(y+20))/(skullB*skullB);
            
            if (skullDist < 1 && y < 80) {
                intensity = 0.9 - skullDist * 0.3;
            }
            
            // Eye sockets (subtract intensity)
            const eyeRadius = 25;
            const leftEyeX = x + 35, leftEyeY = y + 10;
            const rightEyeX = x - 35, rightEyeY = y + 10;
            const leftEyeDist = Math.sqrt(leftEyeX*leftEyeX + leftEyeY*leftEyeY);
            const rightEyeDist = Math.sqrt(rightEyeX*rightEyeX + rightEyeY*rightEyeY);
            
            if (leftEyeDist < eyeRadius) {
                intensity *= (leftEyeDist / eyeRadius) * 0.3;
            }
            if (rightEyeDist < eyeRadius) {
                intensity *= (rightEyeDist / eyeRadius) * 0.3;
            }
            
            // Nasal cavity
            if (Math.abs(x) < 12 && y > 20 && y < 70) {
                const nasalWidth = 12 - (y - 20) * 0.15;
                if (Math.abs(x) < nasalWidth) {
                    intensity *= 0.2;
                }
            }
            
            // Jaw and teeth
            if (y > 60 && y < 120) {
                const jawWidth = Math.max(0, 90 - (y - 60) * 0.8);
                if (Math.abs(x) < jawWidth) {
                    intensity = Math.max(intensity, 0.7);
                    
                    // Teeth pattern
                    if (y > 80 && y < 100 && Math.abs(x) < 60) {
                        const toothPattern = Math.sin(x * 0.3) * 0.3 + 0.7;
                        intensity *= toothPattern;
                    }
                }
            }
            
            // Add some texture variation
            const noise = Math.sin(x * 0.1) * Math.cos(y * 0.1) * 0.1;
            intensity += noise;
            
            return Math.max(0, Math.min(1, intensity));
        }
        
        function animate() {
            time++;
            
            // Clear with fade effect
            ctx.fillStyle = 'rgba(10, 10, 10, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw dots
            dots.forEach(dot => {
                dot.update();
                dot.draw();
            });
            
            // Add atmospheric effects
            if (Math.random() < 0.02) {
                ctx.save();
                ctx.globalAlpha = 0.1;
                ctx.fillStyle = '#ffffff';
                const sparkleX = mouse.x + (Math.random() - 0.5) * 200;
                const sparkleY = mouse.y + (Math.random() - 0.5) * 200;
                ctx.beginPath();
                ctx.arc(sparkleX, sparkleY, Math.random() * 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
            
            requestAnimationFrame(animate);
        }
        
        // Event listeners
        canvas.addEventListener('mousemove', (e) => {
            mouse.x = e.clientX;
            mouse.y = e.clientY;
        });
        
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            dots = [];
            createSkullPattern();
        });
        
        // Initialize
        createSkullPattern();
        animate();
    </script>
</body>
</html>
