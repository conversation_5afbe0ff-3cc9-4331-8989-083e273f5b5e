<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Skull</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: radial-gradient(circle at center, #0a0a0a 0%, #000000 100%);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        canvas {
            display: block;
            cursor: crosshair;
        }
        
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #ffffff;
            font-size: 14px;
            opacity: 0.7;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="info">Move mouse to interact</div>
    <canvas id="canvas"></canvas>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        let mouse = { x: canvas.width / 2, y: canvas.height / 2 };
        let particles = [];
        let backgroundParticles = [];
        let time = 0;

        // 3D skull shape points with depth
        const skullPoints = [
            // Front face outline
            {x: 0, y: -80, z: 0}, {x: 15, y: -85, z: 5}, {x: 30, y: -85, z: 10}, {x: 45, y: -80, z: 15},
            {x: 60, y: -70, z: 20}, {x: 70, y: -55, z: 25}, {x: 75, y: -35, z: 20}, {x: 75, y: -15, z: 15},
            {x: 70, y: 5, z: 10}, {x: 60, y: 20, z: 5}, {x: 45, y: 30, z: 0}, {x: 35, y: 35, z: -5},
            {x: 25, y: 45, z: -10}, {x: 15, y: 50, z: -15}, {x: 0, y: 52, z: -20}, {x: -15, y: 50, z: -15},
            {x: -25, y: 45, z: -10}, {x: -35, y: 35, z: -5}, {x: -45, y: 30, z: 0}, {x: -60, y: 20, z: 5},
            {x: -70, y: 5, z: 10}, {x: -75, y: -15, z: 15}, {x: -75, y: -35, z: 20}, {x: -70, y: -55, z: 25},
            {x: -60, y: -70, z: 20}, {x: -45, y: -80, z: 15}, {x: -30, y: -85, z: 10}, {x: -15, y: -85, z: 5},

            // Eye sockets (deeper)
            {x: -25, y: -25, z: -30}, {x: -20, y: -30, z: -35}, {x: -15, y: -25, z: -30}, {x: -20, y: -20, z: -25},
            {x: 25, y: -25, z: -30}, {x: 20, y: -30, z: -35}, {x: 15, y: -25, z: -30}, {x: 20, y: -20, z: -25},

            // Nasal cavity (recessed)
            {x: 0, y: -10, z: -25}, {x: -5, y: 0, z: -20}, {x: 0, y: 10, z: -15}, {x: 5, y: 0, z: -20},

            // Teeth (protruding)
            {x: -20, y: 35, z: 5}, {x: -10, y: 40, z: 10}, {x: 0, y: 42, z: 15}, {x: 10, y: 40, z: 10}, {x: 20, y: 35, z: 5},

            // Additional depth layers
            {x: 0, y: -60, z: -40}, {x: 20, y: -65, z: -35}, {x: -20, y: -65, z: -35},
            {x: 40, y: -40, z: -30}, {x: -40, y: -40, z: -30},
            {x: 30, y: 0, z: -25}, {x: -30, y: 0, z: -25}
        ];
        
        class Particle {
            constructor(x, y, z = 0, isSkull = false) {
                this.originalX = x;
                this.originalY = y;
                this.originalZ = z;
                this.x = x + (Math.random() - 0.5) * 100;
                this.y = y + (Math.random() - 0.5) * 100;
                this.z = z + (Math.random() - 0.5) * 50;
                this.vx = 0;
                this.vy = 0;
                this.vz = 0;
                this.baseSize = isSkull ? Math.random() * 4 + 3 : Math.random() * 2 + 1;
                this.size = this.baseSize;
                this.opacity = Math.random() * 0.8 + 0.2;
                this.isSkull = isSkull;
                this.baseColor = isSkull ? [255, 220, 180] : [100 + Math.random() * 155, 100 + Math.random() * 155, 255];
                this.life = 1;
                this.decay = isSkull ? 0 : Math.random() * 0.01 + 0.005;
                this.rotationX = Math.random() * Math.PI * 2;
                this.rotationY = Math.random() * Math.PI * 2;
                this.rotationSpeed = (Math.random() - 0.5) * 0.02;
            }
            
            update() {
                if (this.isSkull) {
                    // Skull particles return to formation
                    const dx = this.originalX - this.x;
                    const dy = this.originalY - this.y;
                    this.vx += dx * 0.02;
                    this.vy += dy * 0.02;
                    
                    // Mouse interaction
                    const mouseDistance = Math.sqrt((mouse.x - this.x) ** 2 + (mouse.y - this.y) ** 2);
                    if (mouseDistance < 100) {
                        const force = (100 - mouseDistance) / 100;
                        const angle = Math.atan2(this.y - mouse.y, this.x - mouse.x);
                        this.vx += Math.cos(angle) * force * 2;
                        this.vy += Math.sin(angle) * force * 2;
                    }
                } else {
                    // Background particles float and fade
                    this.vy -= 0.1;
                    this.vx += (Math.random() - 0.5) * 0.1;
                    this.life -= this.decay;
                    
                    if (this.life <= 0) {
                        this.x = Math.random() * canvas.width;
                        this.y = canvas.height + 10;
                        this.life = 1;
                        this.vx = 0;
                        this.vy = 0;
                    }
                }
                
                this.vx *= 0.95;
                this.vy *= 0.95;
                this.x += this.vx;
                this.y += this.vy;
            }
            
            draw() {
                ctx.save();
                ctx.globalAlpha = this.isSkull ? this.opacity : this.life * this.opacity;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                
                // Add glow effect
                if (this.isSkull) {
                    ctx.shadowBlur = 10;
                    ctx.shadowColor = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size * 0.5, 0, Math.PI * 2);
                    ctx.fill();
                }
                ctx.restore();
            }
        }
        
        // Initialize skull particles
        function initSkull() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 2;
            
            skullPoints.forEach(point => {
                for (let i = 0; i < 3; i++) {
                    particles.push(new Particle(
                        centerX + point.x * scale + (Math.random() - 0.5) * 10,
                        centerY + point.y * scale + (Math.random() - 0.5) * 10,
                        true
                    ));
                }
            });
        }
        
        // Initialize background particles
        function initBackground() {
            for (let i = 0; i < 150; i++) {
                backgroundParticles.push(new Particle(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    false
                ));
            }
        }
        
        function animate() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw background particles
            backgroundParticles.forEach(particle => {
                particle.update();
                particle.draw();
            });
            
            // Update and draw skull particles
            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });
            
            // Draw connections between nearby skull particles
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 50) {
                        ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * (1 - distance / 50)})`;
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        ctx.stroke();
                    }
                }
            }
            
            requestAnimationFrame(animate);
        }
        
        // Event listeners
        canvas.addEventListener('mousemove', (e) => {
            mouse.x = e.clientX;
            mouse.y = e.clientY;
        });
        
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
        
        // Initialize and start
        initSkull();
        initBackground();
        animate();
    </script>
</body>
</html>
