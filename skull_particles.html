<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Skull</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: radial-gradient(circle at center, #0a0a0a 0%, #000000 100%);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        canvas {
            display: block;
            cursor: crosshair;
        }
        
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #ffffff;
            font-size: 14px;
            opacity: 0.7;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="info">Move mouse to interact</div>
    <canvas id="canvas"></canvas>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        let mouse = { x: canvas.width / 2, y: canvas.height / 2 };
        let particles = [];
        let backgroundParticles = [];
        
        // Skull shape points (simplified skull outline)
        const skullPoints = [
            // Top of skull
            {x: 0, y: -80}, {x: 15, y: -85}, {x: 30, y: -85}, {x: 45, y: -80},
            {x: 60, y: -70}, {x: 70, y: -55}, {x: 75, y: -35}, {x: 75, y: -15},
            // Right side
            {x: 70, y: 5}, {x: 60, y: 20}, {x: 45, y: 30}, {x: 35, y: 35},
            // Jaw
            {x: 25, y: 45}, {x: 15, y: 50}, {x: 0, y: 52}, {x: -15, y: 50},
            {x: -25, y: 45}, {x: -35, y: 35}, {x: -45, y: 30}, {x: -60, y: 20},
            // Left side
            {x: -70, y: 5}, {x: -75, y: -15}, {x: -75, y: -35}, {x: -70, y: -55},
            {x: -60, y: -70}, {x: -45, y: -80}, {x: -30, y: -85}, {x: -15, y: -85},
            // Eye sockets
            {x: -25, y: -25}, {x: -20, y: -30}, {x: -15, y: -25}, {x: -20, y: -20},
            {x: 25, y: -25}, {x: 20, y: -30}, {x: 15, y: -25}, {x: 20, y: -20},
            // Nasal cavity
            {x: 0, y: -10}, {x: -5, y: 0}, {x: 0, y: 10}, {x: 5, y: 0},
            // Teeth
            {x: -20, y: 35}, {x: -10, y: 40}, {x: 0, y: 42}, {x: 10, y: 40}, {x: 20, y: 35}
        ];
        
        class Particle {
            constructor(x, y, isSkull = false) {
                this.originalX = x;
                this.originalY = y;
                this.x = x + (Math.random() - 0.5) * 100;
                this.y = y + (Math.random() - 0.5) * 100;
                this.vx = 0;
                this.vy = 0;
                this.size = isSkull ? Math.random() * 3 + 2 : Math.random() * 2 + 1;
                this.opacity = Math.random() * 0.8 + 0.2;
                this.isSkull = isSkull;
                this.color = isSkull ? `rgba(255, 255, 255, ${this.opacity})` : 
                           `rgba(${100 + Math.random() * 155}, ${100 + Math.random() * 155}, 255, ${this.opacity * 0.6})`;
                this.life = 1;
                this.decay = isSkull ? 0 : Math.random() * 0.01 + 0.005;
            }
            
            update() {
                if (this.isSkull) {
                    // Skull particles return to formation
                    const dx = this.originalX - this.x;
                    const dy = this.originalY - this.y;
                    this.vx += dx * 0.02;
                    this.vy += dy * 0.02;
                    
                    // Mouse interaction
                    const mouseDistance = Math.sqrt((mouse.x - this.x) ** 2 + (mouse.y - this.y) ** 2);
                    if (mouseDistance < 100) {
                        const force = (100 - mouseDistance) / 100;
                        const angle = Math.atan2(this.y - mouse.y, this.x - mouse.x);
                        this.vx += Math.cos(angle) * force * 2;
                        this.vy += Math.sin(angle) * force * 2;
                    }
                } else {
                    // Background particles float and fade
                    this.vy -= 0.1;
                    this.vx += (Math.random() - 0.5) * 0.1;
                    this.life -= this.decay;
                    
                    if (this.life <= 0) {
                        this.x = Math.random() * canvas.width;
                        this.y = canvas.height + 10;
                        this.life = 1;
                        this.vx = 0;
                        this.vy = 0;
                    }
                }
                
                this.vx *= 0.95;
                this.vy *= 0.95;
                this.x += this.vx;
                this.y += this.vy;
            }
            
            draw() {
                ctx.save();
                ctx.globalAlpha = this.isSkull ? this.opacity : this.life * this.opacity;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                
                // Add glow effect
                if (this.isSkull) {
                    ctx.shadowBlur = 10;
                    ctx.shadowColor = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size * 0.5, 0, Math.PI * 2);
                    ctx.fill();
                }
                ctx.restore();
            }
        }
        
        // Initialize skull particles
        function initSkull() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 2;
            
            skullPoints.forEach(point => {
                for (let i = 0; i < 3; i++) {
                    particles.push(new Particle(
                        centerX + point.x * scale + (Math.random() - 0.5) * 10,
                        centerY + point.y * scale + (Math.random() - 0.5) * 10,
                        true
                    ));
                }
            });
        }
        
        // Initialize background particles
        function initBackground() {
            for (let i = 0; i < 150; i++) {
                backgroundParticles.push(new Particle(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    false
                ));
            }
        }
        
        function animate() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw background particles
            backgroundParticles.forEach(particle => {
                particle.update();
                particle.draw();
            });
            
            // Update and draw skull particles
            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });
            
            // Draw connections between nearby skull particles
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 50) {
                        ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * (1 - distance / 50)})`;
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        ctx.stroke();
                    }
                }
            }
            
            requestAnimationFrame(animate);
        }
        
        // Event listeners
        canvas.addEventListener('mousemove', (e) => {
            mouse.x = e.clientX;
            mouse.y = e.clientY;
        });
        
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
        
        // Initialize and start
        initSkull();
        initBackground();
        animate();
    </script>
</body>
</html>
