<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Particle Skull</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(45deg, #000510 0%, #001122 50%, #000510 100%);
            overflow: hidden;
            font-family: 'Courier New', monospace;
        }

        canvas {
            display: block;
            cursor: crosshair;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #00ff41;
            font-size: 12px;
            opacity: 0.8;
            z-index: 100;
            text-shadow: 0 0 10px #00ff41;
            font-family: 'Courier New', monospace;
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 65, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 65, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="grid-overlay"></div>
    <div class="info">DIGITAL SKULL MATRIX - Move mouse to interact</div>
    <canvas id="canvas"></canvas>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        let mouse = { x: canvas.width / 2, y: canvas.height / 2 };
        let particles = [];
        let backgroundParticles = [];
        let time = 0;

        // 3D skull shape points with depth
        const skullPoints = [
            // Front face outline
            {x: 0, y: -80, z: 0}, {x: 15, y: -85, z: 5}, {x: 30, y: -85, z: 10}, {x: 45, y: -80, z: 15},
            {x: 60, y: -70, z: 20}, {x: 70, y: -55, z: 25}, {x: 75, y: -35, z: 20}, {x: 75, y: -15, z: 15},
            {x: 70, y: 5, z: 10}, {x: 60, y: 20, z: 5}, {x: 45, y: 30, z: 0}, {x: 35, y: 35, z: -5},
            {x: 25, y: 45, z: -10}, {x: 15, y: 50, z: -15}, {x: 0, y: 52, z: -20}, {x: -15, y: 50, z: -15},
            {x: -25, y: 45, z: -10}, {x: -35, y: 35, z: -5}, {x: -45, y: 30, z: 0}, {x: -60, y: 20, z: 5},
            {x: -70, y: 5, z: 10}, {x: -75, y: -15, z: 15}, {x: -75, y: -35, z: 20}, {x: -70, y: -55, z: 25},
            {x: -60, y: -70, z: 20}, {x: -45, y: -80, z: 15}, {x: -30, y: -85, z: 10}, {x: -15, y: -85, z: 5},

            // Eye sockets (deeper)
            {x: -25, y: -25, z: -30}, {x: -20, y: -30, z: -35}, {x: -15, y: -25, z: -30}, {x: -20, y: -20, z: -25},
            {x: 25, y: -25, z: -30}, {x: 20, y: -30, z: -35}, {x: 15, y: -25, z: -30}, {x: 20, y: -20, z: -25},

            // Nasal cavity (recessed)
            {x: 0, y: -10, z: -25}, {x: -5, y: 0, z: -20}, {x: 0, y: 10, z: -15}, {x: 5, y: 0, z: -20},

            // Teeth (protruding)
            {x: -20, y: 35, z: 5}, {x: -10, y: 40, z: 10}, {x: 0, y: 42, z: 15}, {x: 10, y: 40, z: 10}, {x: 20, y: 35, z: 5},

            // Additional depth layers
            {x: 0, y: -60, z: -40}, {x: 20, y: -65, z: -35}, {x: -20, y: -65, z: -35},
            {x: 40, y: -40, z: -30}, {x: -40, y: -40, z: -30},
            {x: 30, y: 0, z: -25}, {x: -30, y: 0, z: -25}
        ];
        
        class Particle {
            constructor(x, y, isSkull = false) {
                this.originalX = x;
                this.originalY = y;
                this.x = x + (Math.random() - 0.5) * 100;
                this.y = y + (Math.random() - 0.5) * 100;
                this.vx = 0;
                this.vy = 0;
                this.size = 1; // Pixel size
                this.opacity = Math.random() * 0.8 + 0.2;
                this.isSkull = isSkull;
                this.life = 1;
                this.decay = isSkull ? 0 : Math.random() * 0.01 + 0.005;
                this.glitchTimer = Math.random() * 100;
                this.digitalNoise = Math.random();
                this.scanlineOffset = Math.random() * 10;

                // Digital color schemes
                if (isSkull) {
                    this.colorType = Math.floor(Math.random() * 3);
                    this.baseColors = [
                        [0, 255, 65],   // Matrix green
                        [255, 255, 255], // White
                        [0, 255, 255]   // Cyan
                    ];
                } else {
                    this.colorType = Math.floor(Math.random() * 4);
                    this.baseColors = [
                        [0, 100, 255],  // Blue
                        [255, 0, 100],  // Pink
                        [100, 255, 0],  // Green
                        [255, 255, 0]   // Yellow
                    ];
                }
            }
            
            update() {
                this.glitchTimer += 0.1;
                this.digitalNoise = Math.sin(this.glitchTimer * 0.1) * 0.5 + 0.5;

                if (this.isSkull) {
                    // Skull particles return to formation with digital distortion
                    const dx = this.originalX - this.x;
                    const dy = this.originalY - this.y;
                    this.vx += dx * 0.02;
                    this.vy += dy * 0.02;

                    // Digital glitch effect
                    if (Math.random() < 0.001) {
                        this.vx += (Math.random() - 0.5) * 10;
                        this.vy += (Math.random() - 0.5) * 10;
                    }

                    // Mouse interaction with digital interference
                    const mouseDistance = Math.sqrt((mouse.x - this.x) ** 2 + (mouse.y - this.y) ** 2);
                    if (mouseDistance < 150) {
                        const force = (150 - mouseDistance) / 150;
                        const angle = Math.atan2(this.y - mouse.y, this.x - mouse.x);
                        this.vx += Math.cos(angle) * force * 3;
                        this.vy += Math.sin(angle) * force * 3;

                        // Digital corruption near mouse
                        if (Math.random() < force * 0.1) {
                            this.colorType = (this.colorType + 1) % this.baseColors.length;
                        }
                    }
                } else {
                    // Background particles - digital rain effect
                    this.vy += 0.5 + Math.sin(time * 0.01 + this.x * 0.01) * 0.2;
                    this.vx += Math.sin(time * 0.005 + this.y * 0.01) * 0.1;
                    this.life -= this.decay;

                    // Digital scanline effect
                    this.scanlineOffset += 0.1;

                    if (this.life <= 0 || this.y > canvas.height + 50) {
                        this.x = Math.random() * canvas.width;
                        this.y = -10;
                        this.life = 1;
                        this.vx = 0;
                        this.vy = 0;
                        this.colorType = Math.floor(Math.random() * this.baseColors.length);
                    }
                }

                this.vx *= 0.98;
                this.vy *= 0.98;
                this.x += this.vx;
                this.y += this.vy;
            }
            
            draw() {
                ctx.save();

                // Get current color with digital effects
                const color = this.baseColors[this.colorType];
                const intensity = this.isSkull ? this.opacity : this.life * this.opacity;
                const digitalGlitch = Math.sin(this.glitchTimer) * 0.3 + 0.7;

                // Digital pixel rendering
                const pixelX = Math.floor(this.x);
                const pixelY = Math.floor(this.y);

                // Scanline effect
                const scanlineIntensity = Math.sin(pixelY * 0.1 + this.scanlineOffset) * 0.2 + 0.8;

                // Final color with digital effects
                const r = Math.floor(color[0] * intensity * digitalGlitch * scanlineIntensity);
                const g = Math.floor(color[1] * intensity * digitalGlitch * scanlineIntensity);
                const b = Math.floor(color[2] * intensity * digitalGlitch * scanlineIntensity);

                ctx.fillStyle = `rgb(${r}, ${g}, ${b})`;

                // Draw pixel
                ctx.fillRect(pixelX, pixelY, this.size, this.size);

                // Add digital glow for skull particles
                if (this.isSkull && Math.random() < 0.3) {
                    ctx.globalAlpha = 0.3;
                    ctx.fillStyle = `rgb(${Math.min(255, r + 50)}, ${Math.min(255, g + 50)}, ${Math.min(255, b + 50)})`;
                    ctx.fillRect(pixelX - 1, pixelY - 1, 3, 3);
                }

                // Random digital artifacts
                if (this.isSkull && Math.random() < 0.01) {
                    ctx.globalAlpha = 0.5;
                    ctx.fillStyle = `rgb(${255}, ${255}, ${255})`;
                    ctx.fillRect(pixelX + Math.floor(Math.random() * 3) - 1, pixelY + Math.floor(Math.random() * 3) - 1, 1, 1);
                }

                ctx.restore();
            }
        }
        
        // Initialize skull particles with higher density for pixel effect
        function initSkull() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 3;

            skullPoints.forEach(point => {
                // Create dense pixel clusters for each skull point
                for (let i = 0; i < 8; i++) {
                    particles.push(new Particle(
                        centerX + point.x * scale + (Math.random() - 0.5) * 15,
                        centerY + point.y * scale + (Math.random() - 0.5) * 15,
                        true
                    ));
                }
            });

            // Add extra pixels for skull density
            for (let i = 0; i < 200; i++) {
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 100 + 50;
                particles.push(new Particle(
                    centerX + Math.cos(angle) * radius,
                    centerY + Math.sin(angle) * radius * 0.8,
                    true
                ));
            }
        }

        // Initialize background particles - digital rain
        function initBackground() {
            for (let i = 0; i < 300; i++) {
                backgroundParticles.push(new Particle(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    false
                ));
            }
        }
        
        function animate() {
            time++;

            // Digital fade effect instead of smooth fade
            ctx.fillStyle = 'rgba(0, 5, 16, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add digital scanlines
            if (time % 3 === 0) {
                ctx.strokeStyle = 'rgba(0, 255, 65, 0.05)';
                ctx.lineWidth = 1;
                for (let y = 0; y < canvas.height; y += 4) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(canvas.width, y);
                    ctx.stroke();
                }
            }

            // Update and draw background particles (digital rain)
            backgroundParticles.forEach(particle => {
                particle.update();
                particle.draw();
            });

            // Update and draw skull particles
            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });

            // Draw digital connections between nearby skull particles
            ctx.globalAlpha = 0.3;
            for (let i = 0; i < particles.length; i += 3) { // Skip some for performance
                for (let j = i + 3; j < particles.length; j += 3) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 30) {
                        const intensity = (1 - distance / 30) * 0.5;
                        ctx.strokeStyle = `rgba(0, 255, 65, ${intensity})`;
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(Math.floor(particles[i].x), Math.floor(particles[i].y));
                        ctx.lineTo(Math.floor(particles[j].x), Math.floor(particles[j].y));
                        ctx.stroke();
                    }
                }
            }
            ctx.globalAlpha = 1;

            // Add random digital glitches
            if (Math.random() < 0.02) {
                ctx.fillStyle = `rgba(0, 255, 65, 0.3)`;
                const glitchX = Math.random() * canvas.width;
                const glitchY = Math.random() * canvas.height;
                ctx.fillRect(glitchX, glitchY, Math.random() * 50, 1);
            }

            requestAnimationFrame(animate);
        }
        
        // Event listeners
        canvas.addEventListener('mousemove', (e) => {
            mouse.x = e.clientX;
            mouse.y = e.clientY;
        });
        
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
        
        // Initialize and start
        initSkull();
        initBackground();
        animate();
    </script>
</body>
</html>
